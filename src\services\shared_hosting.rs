use crate::{
    infrastructure::database::Database,
    models::{User, UserState, PaymentStatus},
    services::{ServiceError, ServiceResult},
};
use mongodb::{
    bson::{doc, oid::ObjectId},
    Collection,
};
use tracing::{info, warn, instrument};
use std::process::Command;
use std::fs;
use std::path::Path;

#[derive(Clone)]
pub struct SharedHostingService {
    users: Collection<User>,
    shared_users: Collection<SharedHostingUser>,
}

#[derive(Debug, Clone, serde::Serialize, serde::Deserialize)]
pub struct SharedHostingUser {
    #[serde(rename = "_id", skip_serializing_if = "Option::is_none")]
    pub id: Option<ObjectId>,
    pub user_id: ObjectId,
    pub username: String,
    pub linux_username: String,
    pub home_directory: String,
    pub state: UserState,
    pub server_id: String,
    pub allocated_cpu_shares: u32,
    pub allocated_memory_mb: u32,
    pub allocated_storage_mb: u32,
    pub current_cpu_usage: f64,
    pub current_memory_usage: f64,
    pub current_storage_usage: f64,
    pub created_at: chrono::DateTime<chrono::Utc>,
    pub updated_at: chrono::DateTime<chrono::Utc>,
    pub last_activity: Option<chrono::DateTime<chrono::Utc>>,
}

#[derive(Debug, serde::Serialize, serde::Deserialize)]
pub struct CreateSharedUserRequest {
    pub user_id: ObjectId,
    pub username: String,
    pub server_id: String,
}

impl SharedHostingService {
    pub fn new(database: Database) -> Self {
        Self {
            users: database.collection("users"),
            shared_users: database.collection("shared_hosting_users"),
        }
    }

    #[instrument(skip(self))]
    pub async fn create_shared_user(&self, request: CreateSharedUserRequest) -> ServiceResult<SharedHostingUser> {
        // Generate unique Linux username
        let linux_username = format!("user_{}", &request.username.replace("-", "_"));
        let home_directory = format!("/home/<USER>/{}", linux_username);

        // Create Linux user with isolated environment
        self.create_linux_user(&linux_username, &home_directory).await?;

        // Set up user directory structure
        self.setup_user_directory(&home_directory, &linux_username).await?;

        // Apply resource limits
        self.apply_resource_limits(&linux_username).await?;

        // Create database record
        let now = chrono::Utc::now();
        let shared_user = SharedHostingUser {
            id: None,
            user_id: request.user_id,
            username: request.username,
            linux_username: linux_username.clone(),
            home_directory: home_directory.clone(),
            state: UserState::Hot,
            server_id: request.server_id,
            allocated_cpu_shares: 1024, // Default CPU shares
            allocated_memory_mb: 512,   // 512MB default
            allocated_storage_mb: 1024, // 1GB default
            current_cpu_usage: 0.0,
            current_memory_usage: 0.0,
            current_storage_usage: 0.0,
            created_at: now,
            updated_at: now,
            last_activity: None,
        };

        let result = self.shared_users.insert_one(&shared_user, None).await?;
        let user_id = result.inserted_id.as_object_id()
            .ok_or_else(|| ServiceError::Internal("Failed to get inserted user ID".to_string()))?;

        let mut created_user = shared_user;
        created_user.id = Some(user_id);

        info!("Created shared hosting user: {} (Linux: {})", created_user.username, linux_username);
        Ok(created_user)
    }

    #[instrument(skip(self))]
    pub async fn update_user_state(&self, user_id: &str, new_state: UserState) -> ServiceResult<()> {
        let object_id = ObjectId::parse_str(user_id)
            .map_err(|_| ServiceError::Validation("Invalid user ID format".to_string()))?;

        // Get user info
        let shared_user = self.shared_users
            .find_one(doc! { "user_id": object_id }, None)
            .await?
            .ok_or_else(|| ServiceError::NotFound("Shared hosting user not found".to_string()))?;

        // Apply state change
        match new_state {
            UserState::Hot => {
                self.enable_user_access(&shared_user.linux_username).await?;
            }
            UserState::Cold => {
                self.disable_user_access(&shared_user.linux_username).await?;
            }
            UserState::Frozen => {
                self.freeze_user_access(&shared_user.linux_username).await?;
            }
            UserState::Migrating => {
                // Prepare for migration
                self.prepare_user_migration(&shared_user.linux_username).await?;
            }
        }

        // Update database
        self.shared_users
            .update_one(
                doc! { "user_id": object_id },
                doc! { 
                    "$set": { 
                        "state": &new_state,
                        "updated_at": chrono::Utc::now()
                    }
                },
                None,
            )
            .await?;

        info!("Updated user {} state to {:?}", shared_user.username, new_state);
        Ok(())
    }

    #[instrument(skip(self))]
    async fn create_linux_user(&self, username: &str, home_dir: &str) -> ServiceResult<()> {
        // Create user with restricted shell and home directory
        let output = Command::new("useradd")
            .args(&[
                "-m",                    // Create home directory
                "-d", home_dir,          // Set home directory
                "-s", "/bin/rbash",      // Restricted bash shell
                "-G", "shared_users",    // Add to shared users group
                username
            ])
            .output()
            .map_err(|e| ServiceError::Internal(format!("Failed to create Linux user: {}", e)))?;

        if !output.status.success() {
            let error = String::from_utf8_lossy(&output.stderr);
            return Err(ServiceError::Internal(format!("useradd failed: {}", error)));
        }

        info!("Created Linux user: {}", username);
        Ok(())
    }

    #[instrument(skip(self))]
    async fn setup_user_directory(&self, home_dir: &str, username: &str) -> ServiceResult<()> {
        // Create directory structure
        let dirs = vec![
            format!("{}/public_html", home_dir),
            format!("{}/logs", home_dir),
            format!("{}/tmp", home_dir),
            format!("{}/backups", home_dir),
        ];

        for dir in dirs {
            fs::create_dir_all(&dir)
                .map_err(|e| ServiceError::Internal(format!("Failed to create directory {}: {}", dir, e)))?;
        }

        // Set proper permissions (user can only access their own files)
        Command::new("chown")
            .args(&["-R", &format!("{}:shared_users", username), home_dir])
            .output()
            .map_err(|e| ServiceError::Internal(format!("Failed to set ownership: {}", e)))?;

        Command::new("chmod")
            .args(&["750", home_dir]) // Owner: rwx, Group: r-x, Others: none
            .output()
            .map_err(|e| ServiceError::Internal(format!("Failed to set permissions: {}", e)))?;

        info!("Set up directory structure for user: {}", username);
        Ok(())
    }

    #[instrument(skip(self))]
    async fn apply_resource_limits(&self, username: &str) -> ServiceResult<()> {
        // Create systemd user slice for resource control
        let slice_content = format!(
            r#"[Unit]
Description=User slice for {}
Before=slices.target

[Slice]
CPUShares=1024
MemoryLimit=512M
TasksMax=50
"#,
            username
        );

        let slice_path = format!("/etc/systemd/system/user-{}.slice", username);
        fs::write(&slice_path, slice_content)
            .map_err(|e| ServiceError::Internal(format!("Failed to create systemd slice: {}", e)))?;

        // Reload systemd
        Command::new("systemctl")
            .args(&["daemon-reload"])
            .output()
            .map_err(|e| ServiceError::Internal(format!("Failed to reload systemd: {}", e)))?;

        info!("Applied resource limits for user: {}", username);
        Ok(())
    }

    #[instrument(skip(self))]
    async fn enable_user_access(&self, username: &str) -> ServiceResult<()> {
        // Unlock user account
        Command::new("usermod")
            .args(&["-U", username])
            .output()
            .map_err(|e| ServiceError::Internal(format!("Failed to unlock user: {}", e)))?;

        info!("Enabled access for user: {}", username);
        Ok(())
    }

    #[instrument(skip(self))]
    async fn disable_user_access(&self, username: &str) -> ServiceResult<()> {
        // Lock user account
        Command::new("usermod")
            .args(&["-L", username])
            .output()
            .map_err(|e| ServiceError::Internal(format!("Failed to lock user: {}", e)))?;

        info!("Disabled access for user: {}", username);
        Ok(())
    }

    #[instrument(skip(self))]
    async fn freeze_user_access(&self, username: &str) -> ServiceResult<()> {
        // Lock account and kill all user processes
        self.disable_user_access(username).await?;

        Command::new("pkill")
            .args(&["-u", username])
            .output()
            .map_err(|e| ServiceError::Internal(format!("Failed to kill user processes: {}", e)))?;

        info!("Froze access for user: {}", username);
        Ok(())
    }

    #[instrument(skip(self))]
    async fn prepare_user_migration(&self, username: &str) -> ServiceResult<()> {
        // Create backup before migration
        let backup_path = format!("/tmp/migration_backup_{}.tar.gz", username);
        let home_dir = format!("/home/<USER>/{}", username);

        Command::new("tar")
            .args(&["-czf", &backup_path, "-C", "/home/<USER>", username])
            .output()
            .map_err(|e| ServiceError::Internal(format!("Failed to create migration backup: {}", e)))?;

        info!("Prepared migration for user: {}", username);
        Ok(())
    }
}
