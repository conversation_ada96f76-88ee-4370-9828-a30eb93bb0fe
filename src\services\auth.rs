use crate::{
    infrastructure::database::Database,
    models::{
        User, CreateUserRequest, LoginRequest, LoginResponse, UserResponse, Claims,
        UserRole, UserStatus, PaymentStatus, HostingPlan,
    },
    services::{ServiceError, ServiceResult, validate_email, validate_username, validate_password},
};
use bcrypt::{hash, verify, DEFAULT_COST};
use chrono::{Duration, Utc};
use jsonwebtoken::{encode, decode, Header, Validation, EncodingKey, DecodingKey};
use mongodb::{
    bson::{doc, oid::ObjectId},
    Collection,
};
use tracing::{info, instrument, warn};

#[derive(Clone)]
pub struct AuthService {
    users: Collection<User>,
    jwt_secret: String,
}

impl AuthService {
    pub fn new(database: Database, jwt_secret: &str) -> Self {
        Self {
            users: database.collection("users"),
            jwt_secret: jwt_secret.to_string(),
        }
    }

    #[instrument(skip(self, request))]
    pub async fn register_user(&self, request: CreateUserRequest) -> ServiceResult<User> {
        // Validate input
        validate_email(&request.email)?;
        validate_username(&request.username)?;
        validate_password(&request.password)?;

        // Check if user already exists
        if self.user_exists_by_email(&request.email).await? {
            return Err(ServiceError::Conflict("User with this email already exists".to_string()));
        }

        if self.user_exists_by_username(&request.username).await? {
            return Err(ServiceError::Conflict("User with this username already exists".to_string()));
        }

        // Hash password
        let password_hash = hash(&request.password, DEFAULT_COST)
            .map_err(|e| ServiceError::Internal(format!("Failed to hash password: {}", e)))?;

        // Create user
        let now = Utc::now();
        let user = User {
            id: None,
            email: request.email,
            username: request.username,
            password_hash,
            first_name: request.first_name,
            last_name: request.last_name,
            role: UserRole::User,
            status: UserStatus::Active,
            payment_status: PaymentStatus::Active,
            hosting_plan: crate::models::HostingPlan::Shared,
            created_at: now,
            updated_at: now,
            last_login: None,
            email_verified: false,
            two_factor_enabled: false,
        };

        let result = self.users.insert_one(&user, None).await?;
        let user_id = result.inserted_id.as_object_id()
            .ok_or_else(|| ServiceError::Internal("Failed to get inserted user ID".to_string()))?;

        let mut created_user = user;
        created_user.id = Some(user_id);

        info!("User registered successfully: {}", created_user.email);
        Ok(created_user)
    }

    #[instrument(skip(self, request))]
    pub async fn login(&self, request: LoginRequest) -> ServiceResult<LoginResponse> {
        // Find user by email
        let user = self.users
            .find_one(doc! { "email": &request.email }, None)
            .await?
            .ok_or_else(|| ServiceError::Unauthorized)?;

        // Verify password
        if !verify(&request.password, &user.password_hash)? {
            warn!("Failed login attempt for user: {}", request.email);
            return Err(ServiceError::Unauthorized);
        }

        // Check user status
        if user.status != UserStatus::Active {
            return Err(ServiceError::Forbidden);
        }

        // Update last login
        self.users
            .update_one(
                doc! { "_id": user.id },
                doc! { "$set": { "last_login": Utc::now() } },
                None,
            )
            .await?;

        // Generate tokens
        let (access_token, refresh_token, expires_in) = self.generate_tokens(&user)?;

        info!("User logged in successfully: {}", user.email);

        Ok(LoginResponse {
            access_token,
            refresh_token,
            user: UserResponse::from(user),
            expires_in,
        })
    }

    #[instrument(skip(self))]
    pub async fn refresh_token(&self, refresh_token: &str) -> ServiceResult<LoginResponse> {
        let claims = self.verify_token(refresh_token)?;
        
        let user = self.get_user_by_id(&claims.sub).await?;
        
        if user.status != UserStatus::Active {
            return Err(ServiceError::Forbidden);
        }

        let (access_token, new_refresh_token, expires_in) = self.generate_tokens(&user)?;

        Ok(LoginResponse {
            access_token,
            refresh_token: new_refresh_token,
            user: UserResponse::from(user),
            expires_in,
        })
    }

    #[instrument(skip(self))]
    pub async fn logout(&self, _token: &str) -> ServiceResult<()> {
        // In a production system, you would add the token to a blacklist
        // For now, we'll just return success
        Ok(())
    }

    #[instrument(skip(self))]
    pub async fn get_user_by_id(&self, user_id: &str) -> ServiceResult<User> {
        let object_id = ObjectId::parse_str(user_id)
            .map_err(|_| ServiceError::Validation("Invalid user ID format".to_string()))?;

        self.users
            .find_one(doc! { "_id": object_id }, None)
            .await?
            .ok_or_else(|| ServiceError::NotFound("User not found".to_string()))
    }

    #[instrument(skip(self))]
    pub fn verify_token(&self, token: &str) -> ServiceResult<Claims> {
        let decoding_key = DecodingKey::from_secret(self.jwt_secret.as_ref());
        let validation = Validation::default();

        decode::<Claims>(token, &decoding_key, &validation)
            .map(|data| data.claims)
            .map_err(|e| ServiceError::Unauthorized)
    }

    async fn user_exists_by_email(&self, email: &str) -> ServiceResult<bool> {
        let count = self.users
            .count_documents(doc! { "email": email }, None)
            .await?;
        Ok(count > 0)
    }

    async fn user_exists_by_username(&self, username: &str) -> ServiceResult<bool> {
        let count = self.users
            .count_documents(doc! { "username": username }, None)
            .await?;
        Ok(count > 0)
    }

    fn generate_tokens(&self, user: &User) -> ServiceResult<(String, String, i64)> {
        let now = Utc::now();
        let expires_in = 3600; // 1 hour
        let refresh_expires_in = 86400 * 7; // 7 days

        let claims = Claims {
            sub: user.id.as_ref().unwrap().to_hex(),
            email: user.email.clone(),
            role: user.role.clone(),
            exp: (now + Duration::seconds(expires_in)).timestamp() as usize,
            iat: now.timestamp() as usize,
        };

        let refresh_claims = Claims {
            sub: user.id.as_ref().unwrap().to_hex(),
            email: user.email.clone(),
            role: user.role.clone(),
            exp: (now + Duration::seconds(refresh_expires_in)).timestamp() as usize,
            iat: now.timestamp() as usize,
        };

        let encoding_key = EncodingKey::from_secret(self.jwt_secret.as_ref());
        let header = Header::default();

        let access_token = encode(&header, &claims, &encoding_key)?;
        let refresh_token = encode(&header, &refresh_claims, &encoding_key)?;

        Ok((access_token, refresh_token, expires_in))
    }
}
