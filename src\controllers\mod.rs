pub mod auth;
pub mod applications;
pub mod plans;
pub mod regions;

use axum::{
    http::StatusCode,
    response::{IntoResponse, Json},
};
use serde_json::json;
use thiserror::Error;
use tracing::error;
use uuid::Uuid;
use chrono::Utc;

use crate::models::{ApiResponse, ResponseMetadata};

/// Controller-level errors
#[derive(Debug, Error)]
pub enum ControllerError {
    #[error("Service error: {0}")]
    Service(#[from] crate::services::ServiceError),
    
    #[error("Validation error: {0}")]
    Validation(String),
    
    #[error("Authentication required")]
    Unauthorized,
    
    #[error("Access forbidden")]
    Forbidden,
    
    #[error("Resource not found: {0}")]
    NotFound(String),
    
    #[error("External API error: {0}")]
    ExternalApi(String),
    
    #[error("Internal server error: {0}")]
    Internal(String),
}

pub type ControllerResult<T> = Result<T, ControllerError>;

impl IntoResponse for ControllerError {
    fn into_response(self) -> axum::response::Response {
        let (status, error_message) = match self {
            ControllerError::Service(ref e) => {
                error!("Service error: {}", e);
                (StatusCode::INTERNAL_SERVER_ERROR, e.to_string())
            }
            ControllerError::Validation(ref msg) => (StatusCode::BAD_REQUEST, msg.clone()),
            ControllerError::Unauthorized => (StatusCode::UNAUTHORIZED, "Authentication required".to_string()),
            ControllerError::Forbidden => (StatusCode::FORBIDDEN, "Access forbidden".to_string()),
            ControllerError::NotFound(ref msg) => (StatusCode::NOT_FOUND, msg.clone()),
            ControllerError::ExternalApi(ref msg) => (StatusCode::BAD_GATEWAY, msg.clone()),
            ControllerError::Internal(ref msg) => {
                error!("Internal error: {}", msg);
                (StatusCode::INTERNAL_SERVER_ERROR, msg.clone())
            }
        };

        let response = error_response(&error_message);
        (status, Json(response)).into_response()
    }
}

/// Create a success response with data
pub fn success_response<T>(data: T) -> Json<ApiResponse<T>> {
    Json(ApiResponse {
        success: true,
        data: Some(data),
        error: None,
        metadata: Some(ResponseMetadata {
            timestamp: Utc::now(),
            request_id: Uuid::new_v4().to_string(),
            version: "1.0".to_string(),
            pagination: None,
        }),
    })
}

/// Create a success response with data and pagination
pub fn success_response_with_pagination<T>(
    data: T,
    pagination: crate::models::PaginationInfo,
) -> Json<ApiResponse<T>> {
    Json(ApiResponse {
        success: true,
        data: Some(data),
        error: None,
        metadata: Some(ResponseMetadata {
            timestamp: Utc::now(),
            request_id: Uuid::new_v4().to_string(),
            version: "1.0".to_string(),
            pagination: Some(pagination),
        }),
    })
}

/// Create an error response
pub fn error_response(error: &str) -> ApiResponse<()> {
    ApiResponse {
        success: false,
        data: None,
        error: Some(error.to_string()),
        metadata: Some(ResponseMetadata {
            timestamp: Utc::now(),
            request_id: Uuid::new_v4().to_string(),
            version: "1.0".to_string(),
            pagination: None,
        }),
    }
}

/// Create a not found response
pub fn not_found_response(resource: &str) -> Json<ApiResponse<()>> {
    Json(error_response(&format!("{} not found", resource)))
}
