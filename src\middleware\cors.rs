use tower_http::cors::{<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>};
use axum::http::{Method, HeaderName};

pub fn cors_layer() -> CorsLayer {
    CorsLayer::new()
        .allow_origin(Any)
        .allow_methods([
            Method::GET,
            Method::POST,
            Method::PUT,
            Method::DELETE,
            Method::PATCH,
            Method::OPTIONS,
        ])
        .allow_headers(Any)
        .allow_credentials(true)
}
