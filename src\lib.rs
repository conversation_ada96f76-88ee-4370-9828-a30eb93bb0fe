pub mod config;
pub mod controllers;
pub mod infrastructure;
pub mod middleware;
pub mod models;
pub mod observability;
pub mod routes;
pub mod services;
pub mod utils;
pub mod vultr;

use infrastructure::{
    circuit_breaker::CircuitBreakerRegistry,
    rate_limiter::RateLimiter,
    metrics::MetricsCollector,
    chunk_processor::ChunkProcessor,
    state_machine::StateMachine,
    database::Database,
};
use services::{
    auth::AuthService,
    billing::BillingService,
    blueprint::BlueprintService,
    build::BuildService,
    deployment::DeploymentService,
    disk::DiskService,
    domain::DomainService,
    environment::EnvironmentService,
    git::GitService,
    instance::InstanceService,
    intelligent_hosting::IntelligentHostingService,
    kubernetes_deployment::KubernetesDeploymentService,
    shared_hosting::SharedHostingService,
    shared_hosting_orchestrator::SharedHostingOrchestrator,
    state_management::StateManagementService,
    ssh_key_management::SSHKeyManagementService,
    resource_allocation::ResourceAllocationService,
    load_balancing::LoadBalancingService,
    payment_access_control::PaymentAccessControlService,
    activity_monitoring::ActivityMonitoringService,
    server_manager::ServerManagerService,
};
use vultr::VultrClient;
use config::Config;

/// Application state shared across all handlers
#[derive(Clone)]
pub struct AppState {
    pub config: Config,
    pub database: Database,
    pub vultr_client: VultrClient,
    
    // Services
    pub auth_service: AuthService,
    pub billing_service: BillingService,
    pub blueprint_service: BlueprintService,
    pub build_service: BuildService,
    pub deployment_service: DeploymentService,
    pub disk_service: DiskService,
    pub domain_service: DomainService,
    pub environment_service: EnvironmentService,
    pub git_service: GitService,
    pub instance_service: InstanceService,
    pub intelligent_hosting_service: IntelligentHostingService,
    pub kubernetes_deployment_service: KubernetesDeploymentService,
    pub shared_hosting_service: SharedHostingService,
    pub shared_hosting_orchestrator: SharedHostingOrchestrator,
    pub state_management_service: StateManagementService,
    pub ssh_key_management_service: SSHKeyManagementService,
    pub resource_allocation_service: ResourceAllocationService,
    pub load_balancing_service: LoadBalancingService,
    pub payment_access_control_service: PaymentAccessControlService,
    pub activity_monitoring_service: ActivityMonitoringService,
    pub server_manager_service: ServerManagerService,
    
    // Infrastructure
    pub circuit_breaker_registry: CircuitBreakerRegistry,
    pub rate_limiter: RateLimiter,
    pub metrics_collector: MetricsCollector,
    pub chunk_processor: ChunkProcessor,
    pub state_machine: StateMachine,
}

// Re-export commonly used types
pub use models::*;
pub use controllers::ControllerError;
pub use services::{ServiceError, ServiceResult};

// Circuit breaker macro for resilient API calls
#[macro_export]
macro_rules! with_circuit_breaker {
    ($registry:expr, $name:expr, $operation:expr) => {{
        let breaker = $registry.get_or_create($name);
        breaker.call($operation).await
    }};
}
