use axum::{
    extract::State,
    response::<PERSON><PERSON>,
};
use std::sync::Arc;
use tracing::instrument;

use crate::{
    AppState,
    models::{
        CreateUserRequest, LoginRequest, LoginResponse, UserResponse,
        ApiResponse,
    },
    controllers::{success_response, ControllerError, ControllerResult},
};

#[instrument(skip(state))]
pub async fn register(
    State(state): State<Arc<AppState>>,
    <PERSON>son(request): J<PERSON><CreateUserRequest>,
) -> ControllerResult<Json<ApiResponse<UserResponse>>> {
    // Validate request
    if request.email.is_empty() {
        return Err(ControllerError::Validation("Email is required".to_string()));
    }
    if request.username.is_empty() {
        return Err(ControllerError::Validation("Username is required".to_string()));
    }
    if request.password.is_empty() {
        return Err(ControllerError::Validation("Password is required".to_string()));
    }
    if request.password.len() < 8 {
        return Err(ControllerError::Validation("Password must be at least 8 characters".to_string()));
    }

    let user = state
        .auth_service
        .register_user(request)
        .await
        .map_err(|e| ControllerError::Service(e))?;

    Ok(success_response(UserResponse::from(user)))
}

#[instrument(skip(state))]
pub async fn login(
    State(state): State<Arc<AppState>>,
    Json(request): Json<LoginRequest>,
) -> ControllerResult<Json<ApiResponse<LoginResponse>>> {
    // Validate request
    if request.email.is_empty() {
        return Err(ControllerError::Validation("Email is required".to_string()));
    }
    if request.password.is_empty() {
        return Err(ControllerError::Validation("Password is required".to_string()));
    }

    let login_response = state
        .auth_service
        .login(request)
        .await
        .map_err(|e| match e {
            crate::services::ServiceError::Unauthorized => ControllerError::Unauthorized,
            _ => ControllerError::Service(e),
        })?;

    Ok(success_response(login_response))
}

#[instrument(skip(state))]
pub async fn refresh_token(
    State(state): State<Arc<AppState>>,
    Json(refresh_token): Json<serde_json::Value>,
) -> ControllerResult<Json<ApiResponse<LoginResponse>>> {
    let token = refresh_token
        .get("refresh_token")
        .and_then(|t| t.as_str())
        .ok_or_else(|| ControllerError::Validation("Refresh token is required".to_string()))?;

    let login_response = state
        .auth_service
        .refresh_token(token)
        .await
        .map_err(|e| match e {
            crate::services::ServiceError::Unauthorized => ControllerError::Unauthorized,
            _ => ControllerError::Service(e),
        })?;

    Ok(success_response(login_response))
}

#[instrument(skip(state))]
pub async fn logout(
    State(state): State<Arc<AppState>>,
    Json(token_data): Json<serde_json::Value>,
) -> ControllerResult<Json<ApiResponse<String>>> {
    let token = token_data
        .get("token")
        .and_then(|t| t.as_str())
        .ok_or_else(|| ControllerError::Validation("Token is required".to_string()))?;

    state
        .auth_service
        .logout(token)
        .await
        .map_err(|e| ControllerError::Service(e))?;

    Ok(success_response("Successfully logged out".to_string()))
}

#[instrument(skip(state))]
pub async fn get_profile(
    State(state): State<Arc<AppState>>,
    claims: crate::models::Claims,
) -> ControllerResult<Json<ApiResponse<UserResponse>>> {
    let user = state
        .auth_service
        .get_user_by_id(&claims.sub)
        .await
        .map_err(|e| ControllerError::Service(e))?;

    Ok(success_response(UserResponse::from(user)))
}

// Alias for get_profile to match route naming
pub async fn get_current_user(
    state: State<Arc<AppState>>,
    claims: crate::models::Claims,
) -> ControllerResult<Json<ApiResponse<UserResponse>>> {
    get_profile(state, claims).await
}
