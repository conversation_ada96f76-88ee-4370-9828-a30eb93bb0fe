use axum::{
    extract::State,
    response::J<PERSON>,
};
use std::sync::Arc;
use tracing::instrument;

use crate::{
    AppState,
    vultr::models::HostingPlan,
    controllers::{success_response, ControllerResult},
};

#[instrument(skip(state))]
pub async fn list_hosting_plans(
    State(state): State<Arc<AppState>>,
) -> ControllerResult<Json<crate::models::ApiResponse<Vec<HostingPlan>>>> {
    let plans = HostingPlan::get_all_plans();
    Ok(success_response(plans))
}

#[instrument(skip(state))]
pub async fn list_shared_plans(
    State(state): State<Arc<AppState>>,
) -> ControllerResult<Json<crate::models::ApiResponse<Vec<HostingPlan>>>> {
    let plans = HostingPlan::get_shared_plans();
    Ok(success_response(plans))
}

#[instrument(skip(state))]
pub async fn list_dedicated_plans(
    State(state): State<Arc<AppState>>,
) -> ControllerResult<Json<crate::models::ApiResponse<Vec<HostingPlan>>>> {
    let plans = HostingPlan::get_dedicated_plans();
    Ok(success_response(plans))
}

#[instrument(skip(state))]
pub async fn list_enterprise_plans(
    State(state): State<Arc<AppState>>,
) -> ControllerResult<Json<crate::models::ApiResponse<Vec<HostingPlan>>>> {
    let plans = HostingPlan::get_enterprise_plans();
    Ok(success_response(plans))
}
