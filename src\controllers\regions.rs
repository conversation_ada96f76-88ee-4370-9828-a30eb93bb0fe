use axum::{
    extract::State,
    response::<PERSON><PERSON>,
};
use std::sync::Arc;
use tracing::instrument;

use crate::{
    AppState,
    controllers::{success_response, ControllerError, ControllerResult},
};

#[derive(serde::Serialize)]
pub struct Region {
    pub id: String,
    pub name: String,
    pub country: String,
    pub continent: String,
    pub available: bool,
}

#[instrument(skip(state))]
pub async fn list_regions(
    State(state): State<Arc<AppState>>,
) -> ControllerResult<Json<crate::models::ApiResponse<Vec<Region>>>> {
    // For African market focus, provide user-friendly region names
    let regions = vec![
        Region {
            id: "ewr".to_string(),
            name: "New Jersey, USA".to_string(),
            country: "United States".to_string(),
            continent: "North America".to_string(),
            available: true,
        },
        Region {
            id: "lhr".to_string(),
            name: "London, UK".to_string(),
            country: "United Kingdom".to_string(),
            continent: "Europe".to_string(),
            available: true,
        },
        Region {
            id: "fra".to_string(),
            name: "Frankfurt, Germany".to_string(),
            country: "Germany".to_string(),
            continent: "Europe".to_string(),
            available: true,
        },
        Region {
            id: "sgp".to_string(),
            name: "Singapore".to_string(),
            country: "Singapore".to_string(),
            continent: "Asia".to_string(),
            available: true,
        },
        Region {
            id: "syd".to_string(),
            name: "Sydney, Australia".to_string(),
            country: "Australia".to_string(),
            continent: "Oceania".to_string(),
            available: true,
        },
        Region {
            id: "jnb".to_string(),
            name: "Johannesburg, South Africa".to_string(),
            country: "South Africa".to_string(),
            continent: "Africa".to_string(),
            available: true,
        },
    ];

    Ok(success_response(regions))
}
