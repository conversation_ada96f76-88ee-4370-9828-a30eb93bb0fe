use super::{VultrApiClient, Result, VultrPlansResponse, VultrRegionsResponse, VultrInstancesResponse, VultrInstance, CreateInstanceRequest};
use tracing::instrument;

impl VultrApiClient {
    #[instrument(skip(self))]
    pub async fn list_plans(&self) -> Result<VultrPlansResponse> {
        self.get("/plans").await
    }

    #[instrument(skip(self))]
    pub async fn list_regions(&self) -> Result<VultrRegionsResponse> {
        self.get("/regions").await
    }

    #[instrument(skip(self))]
    pub async fn list_instances(&self) -> Result<VultrInstancesResponse> {
        self.get("/instances").await
    }

    #[instrument(skip(self))]
    pub async fn get_instance(&self, instance_id: &str) -> Result<VultrInstance> {
        let endpoint = format!("/instances/{}", instance_id);
        self.get(&endpoint).await
    }

    #[instrument(skip(self, request))]
    pub async fn create_instance(&self, request: &CreateInstanceRequest) -> Result<VultrInstance> {
        self.post("/instances", request).await
    }

    #[instrument(skip(self))]
    pub async fn delete_instance(&self, instance_id: &str) -> Result<()> {
        let endpoint = format!("/instances/{}", instance_id);
        self.delete(&endpoint).await
    }

    #[instrument(skip(self))]
    pub async fn start_instance(&self, instance_id: &str) -> Result<()> {
        let endpoint = format!("/instances/{}/start", instance_id);
        self.post(&endpoint, &serde_json::json!({})).await
    }

    #[instrument(skip(self))]
    pub async fn stop_instance(&self, instance_id: &str) -> Result<()> {
        let endpoint = format!("/instances/{}/halt", instance_id);
        self.post(&endpoint, &serde_json::json!({})).await
    }

    #[instrument(skip(self))]
    pub async fn restart_instance(&self, instance_id: &str) -> Result<()> {
        let endpoint = format!("/instances/{}/reboot", instance_id);
        self.post(&endpoint, &serde_json::json!({})).await
    }

    #[instrument(skip(self))]
    pub async fn get_instance_neighbors(&self, instance_id: &str) -> Result<Vec<String>> {
        let endpoint = format!("/instances/{}/neighbors", instance_id);
        let response: serde_json::Value = self.get(&endpoint).await?;
        
        // Extract neighbors from response
        if let Some(neighbors) = response.get("neighbors").and_then(|n| n.as_array()) {
            Ok(neighbors.iter()
                .filter_map(|n| n.as_str().map(|s| s.to_string()))
                .collect())
        } else {
            Ok(vec![])
        }
    }

    #[instrument(skip(self))]
    pub async fn get_instance_upgrades(&self, instance_id: &str) -> Result<Vec<String>> {
        let endpoint = format!("/instances/{}/upgrades", instance_id);
        let response: serde_json::Value = self.get(&endpoint).await?;
        
        // Extract upgrades from response
        if let Some(upgrades) = response.get("upgrades").and_then(|u| u.as_array()) {
            Ok(upgrades.iter()
                .filter_map(|u| u.get("plan").and_then(|p| p.as_str()).map(|s| s.to_string()))
                .collect())
        } else {
            Ok(vec![])
        }
    }
}

// Re-export for backward compatibility
pub use super::VultrApiClient as VultrClient;
