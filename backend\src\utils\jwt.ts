import jwt from 'jsonwebtoken';
import { JWTClaims, UserRole } from '@/types';

export class JWTService {
  private static readonly ACCESS_TOKEN_EXPIRES_IN = process.env.JWT_EXPIRES_IN || '24h';
  private static readonly REFRESH_TOKEN_EXPIRES_IN = process.env.JWT_REFRESH_EXPIRES_IN || '7d';
  private static readonly JWT_SECRET = process.env.JWT_SECRET;

  static generateAccessToken(userId: string, email: string, role: UserRole): string {
    if (!this.JWT_SECRET) {
      throw new Error('JWT_SECRET environment variable is not set');
    }

    const payload: Omit<JWTClaims, 'exp' | 'iat'> = {
      sub: userId,
      email,
      role
    };

    return jwt.sign(payload, this.JWT_SECRET, {
      expiresIn: this.ACCESS_TOKEN_EXPIRES_IN,
      issuer: 'achidas-api',
      audience: 'achidas-client'
    } as jwt.SignOptions);
  }

  static generateRefreshToken(userId: string): string {
    if (!this.JWT_SECRET) {
      throw new Error('JWT_SECRET environment variable is not set');
    }

    return jwt.sign(
      { sub: userId, type: 'refresh' },
      this.JWT_SECRET,
      {
        expiresIn: this.REFRESH_TOKEN_EXPIRES_IN,
        issuer: 'achidas-api',
        audience: 'achidas-client'
      } as jwt.SignOptions
    );
  }

  static verifyAccessToken(token: string): JWTClaims {
    if (!this.JWT_SECRET) {
      throw new Error('JWT_SECRET environment variable is not set');
    }

    return jwt.verify(token, this.JWT_SECRET, {
      issuer: 'achidas-api',
      audience: 'achidas-client'
    }) as JWTClaims;
  }

  static verifyRefreshToken(token: string): { sub: string; type: string } {
    if (!this.JWT_SECRET) {
      throw new Error('JWT_SECRET environment variable is not set');
    }

    const decoded = jwt.verify(token, this.JWT_SECRET, {
      issuer: 'achidas-api',
      audience: 'achidas-client'
    }) as any;

    if (decoded.type !== 'refresh') {
      throw new Error('Invalid refresh token');
    }

    return decoded;
  }

  static getTokenExpirationTime(expiresIn: string = this.ACCESS_TOKEN_EXPIRES_IN): number {
    // Convert string like '24h', '7d', '60s' to seconds
    const unit = expiresIn.slice(-1);
    const value = parseInt(expiresIn.slice(0, -1));

    switch (unit) {
      case 's':
        return value;
      case 'm':
        return value * 60;
      case 'h':
        return value * 60 * 60;
      case 'd':
        return value * 24 * 60 * 60;
      default:
        return 24 * 60 * 60; // Default to 24 hours
    }
  }
}
