pub mod auth;
pub mod shared_hosting;

use thiserror::Error;
use mongodb::bson::oid::ObjectId;

/// Service-level errors
#[derive(Debug, Error)]
pub enum ServiceError {
    #[error("Database error: {0}")]
    Database(#[from] mongodb::error::Error),
    
    #[error("Validation error: {0}")]
    Validation(String),
    
    #[error("Not found: {0}")]
    NotFound(String),
    
    #[error("Unauthorized access")]
    Unauthorized,
    
    #[error("Forbidden access")]
    Forbidden,
    
    #[error("Conflict: {0}")]
    Conflict(String),
    
    #[error("External API error: {0}")]
    ExternalApi(String),
    
    #[error("Configuration error: {0}")]
    Configuration(String),
    
    #[error("Network error: {0}")]
    Network(#[from] reqwest::Error),
    
    #[error("Serialization error: {0}")]
    Serialization(#[from] serde_json::Error),
    
    #[error("JWT error: {0}")]
    Jwt(#[from] jsonwebtoken::errors::Error),
    
    #[error("Bcrypt error: {0}")]
    Bcrypt(#[from] bcrypt::BcryptError),
    
    #[error("IO error: {0}")]
    Io(#[from] std::io::Error),
    
    #[error("Internal error: {0}")]
    Internal(String),
}

pub type ServiceResult<T> = Result<T, ServiceError>;

/// Convert ObjectId to string safely
pub fn object_id_to_string(id: &ObjectId) -> String {
    id.to_hex()
}

/// Parse string to ObjectId safely
pub fn string_to_object_id(id: &str) -> ServiceResult<ObjectId> {
    ObjectId::parse_str(id).map_err(|_| ServiceError::Validation("Invalid ObjectId format".to_string()))
}

/// Validate email format
pub fn validate_email(email: &str) -> ServiceResult<()> {
    let email_regex = regex::Regex::new(r"^[^\s@]+@[^\s@]+\.[^\s@]+$")
        .map_err(|e| ServiceError::Internal(format!("Regex error: {}", e)))?;
    
    if email_regex.is_match(email) {
        Ok(())
    } else {
        Err(ServiceError::Validation("Invalid email format".to_string()))
    }
}

/// Validate username format
pub fn validate_username(username: &str) -> ServiceResult<()> {
    if username.len() < 3 {
        return Err(ServiceError::Validation("Username must be at least 3 characters".to_string()));
    }
    if username.len() > 30 {
        return Err(ServiceError::Validation("Username must be at most 30 characters".to_string()));
    }
    
    let username_regex = regex::Regex::new(r"^[a-zA-Z0-9_-]+$")
        .map_err(|e| ServiceError::Internal(format!("Regex error: {}", e)))?;
    
    if username_regex.is_match(username) {
        Ok(())
    } else {
        Err(ServiceError::Validation("Username can only contain letters, numbers, underscores, and hyphens".to_string()))
    }
}

/// Validate password strength
pub fn validate_password(password: &str) -> ServiceResult<()> {
    if password.len() < 8 {
        return Err(ServiceError::Validation("Password must be at least 8 characters".to_string()));
    }
    if password.len() > 128 {
        return Err(ServiceError::Validation("Password must be at most 128 characters".to_string()));
    }
    
    let has_uppercase = password.chars().any(|c| c.is_uppercase());
    let has_lowercase = password.chars().any(|c| c.is_lowercase());
    let has_digit = password.chars().any(|c| c.is_numeric());
    let has_special = password.chars().any(|c| "!@#$%^&*()_+-=[]{}|;:,.<>?".contains(c));
    
    if !has_uppercase {
        return Err(ServiceError::Validation("Password must contain at least one uppercase letter".to_string()));
    }
    if !has_lowercase {
        return Err(ServiceError::Validation("Password must contain at least one lowercase letter".to_string()));
    }
    if !has_digit {
        return Err(ServiceError::Validation("Password must contain at least one digit".to_string()));
    }
    if !has_special {
        return Err(ServiceError::Validation("Password must contain at least one special character".to_string()));
    }
    
    Ok(())
}

/// Common pagination helper
pub fn calculate_pagination(page: u32, per_page: u32, total: u64) -> crate::models::PaginationInfo {
    let total_pages = ((total as f64) / (per_page as f64)).ceil() as u32;
    
    crate::models::PaginationInfo {
        page,
        per_page,
        total,
        total_pages,
    }
}
