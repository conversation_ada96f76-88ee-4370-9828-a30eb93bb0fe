use axum::{
    extract::{State, Path, Query},
    response::<PERSON><PERSON>,
};
use std::sync::Arc;
use tracing::instrument;

use crate::{
    AppState,
    models::{
        CreateApplicationRequest, CreateSimpleApplicationRequest, UpdateApplicationRequest,
        ApplicationResponse, PaginationQuery, Claims, ServiceType, HostingPlan,
    },
    controllers::{success_response, success_response_with_pagination, not_found_response, ControllerError, ControllerResult},
    services::calculate_pagination,
};

#[instrument(skip(state))]
pub async fn list_applications(
    State(state): State<Arc<AppState>>,
    Query(pagination): Query<PaginationQuery>,
    claims: Claims,
) -> ControllerResult<Json<crate::models::ApiResponse<Vec<ApplicationResponse>>>> {
    let page = pagination.page.unwrap_or(1);
    let per_page = pagination.per_page.unwrap_or(20);
    
    // For now, return mock data - you'll implement the actual service later
    let applications = vec![
        ApplicationResponse {
            id: "app1".to_string(),
            name: "My Web App".to_string(),
            description: Some("A sample web application".to_string()),
            repository_url: Some("https://github.com/user/repo".to_string()),
            branch: "main".to_string(),
            build_command: Some("npm run build".to_string()),
            install_command: Some("npm install".to_string()),
            start_command: Some("npm start".to_string()),
            environment_variables: std::collections::HashMap::new(),
            service_type: ServiceType::WebService,
            status: crate::models::ServiceStatus::Running,
            hosting_plan: HostingPlan::Shared,
            region: "ewr".to_string(),
            created_at: chrono::Utc::now(),
            updated_at: chrono::Utc::now(),
            last_deployed_at: Some(chrono::Utc::now()),
            auto_deploy: true,
            custom_domain: None,
            ssl_enabled: true,
            url: Some("https://my-web-app.achidas.app".to_string()),
        }
    ];

    let total = applications.len() as u64;
    let pagination_info = calculate_pagination(page, per_page, total);

    Ok(success_response_with_pagination(applications, pagination_info))
}

#[instrument(skip(state))]
pub async fn get_application(
    State(state): State<Arc<AppState>>,
    Path(id): Path<String>,
    claims: Claims,
) -> ControllerResult<Json<crate::models::ApiResponse<ApplicationResponse>>> {
    // For now, return mock data
    let application = ApplicationResponse {
        id: id.clone(),
        name: "My Web App".to_string(),
        description: Some("A sample web application".to_string()),
        repository_url: Some("https://github.com/user/repo".to_string()),
        branch: "main".to_string(),
        build_command: Some("npm run build".to_string()),
        install_command: Some("npm install".to_string()),
        start_command: Some("npm start".to_string()),
        environment_variables: std::collections::HashMap::new(),
        service_type: ServiceType::WebService,
        status: crate::models::ServiceStatus::Running,
        hosting_plan: HostingPlan::Shared,
        region: "ewr".to_string(),
        created_at: chrono::Utc::now(),
        updated_at: chrono::Utc::now(),
        last_deployed_at: Some(chrono::Utc::now()),
        auto_deploy: true,
        custom_domain: None,
        ssl_enabled: true,
        url: Some("https://my-web-app.achidas.app".to_string()),
    };

    Ok(success_response(application))
}

#[instrument(skip(state))]
pub async fn create_application(
    State(state): State<Arc<AppState>>,
    claims: Claims,
    Json(request): Json<CreateApplicationRequest>,
) -> ControllerResult<Json<crate::models::ApiResponse<ApplicationResponse>>> {
    // Validate request
    if request.name.is_empty() {
        return Err(ControllerError::Validation("Application name is required".to_string()));
    }

    // For now, return mock created application
    let application = ApplicationResponse {
        id: uuid::Uuid::new_v4().to_string(),
        name: request.name,
        description: request.description,
        repository_url: request.repository_url,
        branch: request.branch.unwrap_or_else(|| "main".to_string()),
        build_command: request.build_command,
        install_command: request.install_command,
        start_command: request.start_command,
        environment_variables: request.environment_variables.unwrap_or_default(),
        service_type: request.service_type,
        status: crate::models::ServiceStatus::Pending,
        hosting_plan: request.hosting_plan,
        region: request.region.unwrap_or_else(|| "ewr".to_string()),
        created_at: chrono::Utc::now(),
        updated_at: chrono::Utc::now(),
        last_deployed_at: None,
        auto_deploy: request.auto_deploy.unwrap_or(false),
        custom_domain: request.custom_domain,
        ssl_enabled: true,
        url: None,
    };

    Ok(success_response(application))
}

#[instrument(skip(state))]
pub async fn create_simple_application(
    State(state): State<Arc<AppState>>,
    claims: Claims,
    Json(request): Json<CreateSimpleApplicationRequest>,
) -> ControllerResult<Json<crate::models::ApiResponse<ApplicationResponse>>> {
    // Validate request
    if request.name.is_empty() {
        return Err(ControllerError::Validation("Application name is required".to_string()));
    }
    if request.repository_url.is_empty() {
        return Err(ControllerError::Validation("Repository URL is required".to_string()));
    }

    // Create application with default settings
    let application = ApplicationResponse {
        id: uuid::Uuid::new_v4().to_string(),
        name: request.name,
        description: None,
        repository_url: Some(request.repository_url),
        branch: request.branch.unwrap_or_else(|| "main".to_string()),
        build_command: None,
        install_command: None,
        start_command: None,
        environment_variables: std::collections::HashMap::new(),
        service_type: request.service_type,
        status: crate::models::ServiceStatus::Pending,
        hosting_plan: HostingPlan::Shared, // Default to shared hosting
        region: "ewr".to_string(), // Default region
        created_at: chrono::Utc::now(),
        updated_at: chrono::Utc::now(),
        last_deployed_at: None,
        auto_deploy: true, // Enable auto-deploy by default
        custom_domain: None,
        ssl_enabled: true,
        url: None,
    };

    Ok(success_response(application))
}

#[instrument(skip(state))]
pub async fn update_application(
    State(state): State<Arc<AppState>>,
    Path(id): Path<String>,
    claims: Claims,
    Json(request): Json<UpdateApplicationRequest>,
) -> ControllerResult<Json<crate::models::ApiResponse<ApplicationResponse>>> {
    // For now, return mock updated application
    let application = ApplicationResponse {
        id: id.clone(),
        name: request.name.unwrap_or_else(|| "Updated App".to_string()),
        description: request.description,
        repository_url: request.repository_url,
        branch: request.branch.unwrap_or_else(|| "main".to_string()),
        build_command: request.build_command,
        install_command: request.install_command,
        start_command: request.start_command,
        environment_variables: request.environment_variables.unwrap_or_default(),
        service_type: ServiceType::WebService,
        status: crate::models::ServiceStatus::Running,
        hosting_plan: HostingPlan::Shared,
        region: "ewr".to_string(),
        created_at: chrono::Utc::now() - chrono::Duration::days(1),
        updated_at: chrono::Utc::now(),
        last_deployed_at: Some(chrono::Utc::now()),
        auto_deploy: request.auto_deploy.unwrap_or(false),
        custom_domain: request.custom_domain,
        ssl_enabled: true,
        url: Some("https://updated-app.achidas.app".to_string()),
    };

    Ok(success_response(application))
}

#[instrument(skip(state))]
pub async fn delete_application(
    State(state): State<Arc<AppState>>,
    Path(id): Path<String>,
    claims: Claims,
) -> ControllerResult<Json<crate::models::ApiResponse<String>>> {
    // For now, just return success
    Ok(success_response(format!("Application {} deleted successfully", id)))
}
