use serde::{Deserialize, Serialize};
use chrono::{DateTime, Utc};

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct VultrPlan {
    pub id: String,
    pub vcpu_count: u32,
    pub ram: u64,
    pub disk: u64,
    pub bandwidth: u64,
    pub monthly_cost: f64,
    pub type_: String,
    pub locations: Vec<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VultrRegion {
    pub id: String,
    pub city: String,
    pub country: String,
    pub continent: String,
    pub options: Vec<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VultrInstance {
    pub id: String,
    pub os: String,
    pub ram: u64,
    pub disk: u64,
    pub main_ip: String,
    pub vcpu_count: u32,
    pub region: String,
    pub plan: String,
    pub date_created: String,
    pub status: String,
    pub allowed_bandwidth: u64,
    pub netmask_v4: String,
    pub gateway_v4: String,
    pub power_status: String,
    pub server_status: String,
    pub v6_network: String,
    pub v6_main_ip: String,
    pub v6_network_size: u32,
    pub label: String,
    pub internal_ip: String,
    pub kvm: String,
    pub hostname: String,
    pub tag: String,
    pub os_id: u32,
    pub app_id: u32,
    pub image_id: String,
    pub firewall_group_id: String,
    pub features: Vec<String>,
    pub user_data: String,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct CreateInstanceRequest {
    pub region: String,
    pub plan: String,
    pub label: Option<String>,
    pub os_id: Option<u32>,
    pub hostname: Option<String>,
    pub tag: Option<String>,
    pub user_data: Option<String>,
    pub enable_ipv6: Option<bool>,
    pub enable_private_network: Option<bool>,
    pub attach_private_network: Option<Vec<String>>,
    pub auto_backup: Option<bool>,
    pub ddos_protection: Option<bool>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct VultrPlansResponse {
    pub plans: Vec<VultrPlan>,
    pub meta: VultrMeta,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct VultrRegionsResponse {
    pub regions: Vec<VultrRegion>,
    pub meta: VultrMeta,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct VultrInstancesResponse {
    pub instances: Vec<VultrInstance>,
    pub meta: VultrMeta,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct VultrMeta {
    pub total: u32,
    pub links: VultrLinks,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct VultrLinks {
    pub next: Option<String>,
    pub prev: Option<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct HostingPlan {
    pub id: String,
    pub name: String,
    pub tier: HostingTier,
    pub vcpu: u32,
    pub ram_gb: u32,
    pub storage_gb: u32,
    pub bandwidth_gb: u32,
    pub price_monthly: f64,
    pub max_applications: u32,
    pub features: Vec<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub enum HostingTier {
    Shared,
    Dedicated,
    Enterprise,
}

impl HostingPlan {
    pub fn get_shared_plans() -> Vec<HostingPlan> {
        vec![
            HostingPlan {
                id: "shared-starter".to_string(),
                name: "Shared Starter".to_string(),
                tier: HostingTier::Shared,
                vcpu: 1,
                ram_gb: 1,
                storage_gb: 25,
                bandwidth_gb: 1000,
                price_monthly: 5.0,
                max_applications: 3,
                features: vec![
                    "SSL Certificate".to_string(),
                    "24/7 Support".to_string(),
                    "99.9% Uptime".to_string(),
                ],
            },
            HostingPlan {
                id: "shared-pro".to_string(),
                name: "Shared Pro".to_string(),
                tier: HostingTier::Shared,
                vcpu: 2,
                ram_gb: 2,
                storage_gb: 50,
                bandwidth_gb: 2000,
                price_monthly: 10.0,
                max_applications: 10,
                features: vec![
                    "SSL Certificate".to_string(),
                    "24/7 Support".to_string(),
                    "99.9% Uptime".to_string(),
                    "Custom Domain".to_string(),
                ],
            },
        ]
    }

    pub fn get_dedicated_plans() -> Vec<HostingPlan> {
        vec![
            HostingPlan {
                id: "dedicated-basic".to_string(),
                name: "Dedicated Basic".to_string(),
                tier: HostingTier::Dedicated,
                vcpu: 2,
                ram_gb: 4,
                storage_gb: 80,
                bandwidth_gb: 3000,
                price_monthly: 25.0,
                max_applications: 50,
                features: vec![
                    "Dedicated Resources".to_string(),
                    "SSL Certificate".to_string(),
                    "24/7 Support".to_string(),
                    "99.9% Uptime".to_string(),
                    "Custom Domain".to_string(),
                    "SSH Access".to_string(),
                ],
            },
        ]
    }

    pub fn get_enterprise_plans() -> Vec<HostingPlan> {
        vec![
            HostingPlan {
                id: "enterprise-standard".to_string(),
                name: "Enterprise Standard".to_string(),
                tier: HostingTier::Enterprise,
                vcpu: 8,
                ram_gb: 16,
                storage_gb: 200,
                bandwidth_gb: 10000,
                price_monthly: 100.0,
                max_applications: 1000,
                features: vec![
                    "Dedicated Resources".to_string(),
                    "SSL Certificate".to_string(),
                    "24/7 Priority Support".to_string(),
                    "99.99% Uptime SLA".to_string(),
                    "Custom Domain".to_string(),
                    "SSH Access".to_string(),
                    "Load Balancing".to_string(),
                    "Auto Scaling".to_string(),
                ],
            },
        ]
    }

    pub fn get_all_plans() -> Vec<HostingPlan> {
        let mut plans = Vec::new();
        plans.extend(Self::get_shared_plans());
        plans.extend(Self::get_dedicated_plans());
        plans.extend(Self::get_enterprise_plans());
        plans
    }
}
