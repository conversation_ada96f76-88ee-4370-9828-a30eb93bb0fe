use axum::{
    routing::{get, post, put, delete},
    Router,
};
use std::sync::Arc;
use crate::{
    AppState,
    controllers::{
        auth,
        applications,
        billing,
        deployments,
        disks,
        domains,
        environments,
        git,
        instances,
        intelligent_hosting,
        kubernetes,
        logs,
        regions,
        server_manager,
        shared_hosting,
        vultr,
        webhooks,
    },
    middleware::{auth::auth_middleware, cors::cors_layer},
};

pub fn create_routes() -> Router<Arc<AppState>> {
    Router::new()
        // Health check
        .route("/health", get(health_check))
        
        // Public routes (no authentication required)
        .nest("/api/v1/public", public_routes())
        
        // User routes (authentication required)
        .nest("/api/v1/users", user_routes())
        
        // Admin routes (admin authentication required)
        .nest("/api/v1/admin", admin_routes())
        
        // Webhooks
        .nest("/webhooks", webhook_routes())
        
        .layer(cors_layer())
}

fn public_routes() -> Router<Arc<AppState>> {
    Router::new()
        .route("/auth/login", post(auth::login))
        .route("/auth/register", post(auth::register))
        .route("/auth/refresh", post(auth::refresh_token))
        .route("/regions", get(regions::list_regions))
        .route("/plans", get(vultr::list_plans))
}

fn user_routes() -> Router<Arc<AppState>> {
    Router::new()
        // Applications
        .route("/applications", get(applications::list_applications))
        .route("/applications", post(applications::create_application))
        .route("/applications/simple", post(applications::create_simple_application))
        .route("/applications/:id", get(applications::get_application))
        .route("/applications/:id", put(applications::update_application))
        .route("/applications/:id", delete(applications::delete_application))
        
        // Deployments
        .route("/deployments", get(deployments::list_deployments))
        .route("/deployments", post(deployments::create_deployment))
        .route("/deployments/:id", get(deployments::get_deployment))
        .route("/deployments/:id/trigger", post(deployments::trigger_deployment))
        .route("/deployments/:id/logs", get(logs::get_deployment_logs))
        
        // Environments
        .route("/environments", get(environments::list_environments))
        .route("/environments", post(environments::create_environment))
        .route("/environments/:id", get(environments::get_environment))
        .route("/environments/:id", put(environments::update_environment))
        .route("/environments/:id", delete(environments::delete_environment))
        
        // Git repositories
        .route("/git/repositories", get(git::list_repositories))
        .route("/git/repositories", post(git::create_repository))
        .route("/git/repositories/:id", get(git::get_repository))
        .route("/git/repositories/:id", put(git::update_repository))
        .route("/git/repositories/:id", delete(git::delete_repository))
        
        // Shared hosting
        .route("/shared-hosting/servers", get(shared_hosting::list_shared_servers))
        .route("/shared-hosting/users", get(shared_hosting::list_shared_users))
        .route("/shared-hosting/users/:id/state", put(shared_hosting::update_user_state))
        
        // Intelligent hosting
        .route("/intelligent-hosting/recommendations", get(intelligent_hosting::get_hosting_recommendations))
        .route("/intelligent-hosting/provision", post(intelligent_hosting::provision_intelligent_hosting))
        
        // Kubernetes
        .route("/kubernetes/clusters", get(kubernetes::list_clusters))
        .route("/kubernetes/clusters", post(kubernetes::create_cluster))
        .route("/kubernetes/clusters/:id", get(kubernetes::get_cluster))
        .route("/kubernetes/clusters/:id", delete(kubernetes::delete_cluster))
        
        // Billing
        .route("/billing/invoices", get(billing::list_invoices))
        .route("/billing/pending-charges", get(billing::get_pending_charges))
        .route("/billing/usage", get(billing::get_usage_summary))
        
        .layer(auth_middleware())
}

fn admin_routes() -> Router<Arc<AppState>> {
    Router::new()
        // Server management
        .route("/servers", get(server_manager::list_servers))
        .route("/servers", post(server_manager::create_server))
        .route("/servers/:id", get(server_manager::get_server))
        .route("/servers/:id", put(server_manager::update_server))
        .route("/servers/:id", delete(server_manager::delete_server))
        .route("/servers/sync", post(server_manager::sync_vultr_servers))
        
        // Vultr API endpoints
        .nest("/vultr", vultr_routes())
        
        .layer(auth_middleware())
}

fn vultr_routes() -> Router<Arc<AppState>> {
    Router::new()
        // Instances
        .route("/instances", get(vultr::list_instances))
        .route("/instances", post(vultr::create_instance))
        .route("/instances/:id", get(vultr::get_instance))
        .route("/instances/:id", delete(vultr::delete_instance))
        .route("/instances/:id/start", post(vultr::start_instance))
        .route("/instances/:id/stop", post(vultr::stop_instance))
        .route("/instances/:id/restart", post(vultr::restart_instance))
        .route("/instances/:id/bandwidth", get(vultr::get_instance_bandwidth))
        .route("/instances/:id/neighbors", get(vultr::get_instance_neighbors))
        .route("/instances/:id/upgrades", get(vultr::get_instance_upgrades))
        
        // Block storage
        .route("/block-storage", get(vultr::list_block_storage))
        .route("/block-storage", post(vultr::create_block_storage))
        .route("/block-storage/:id", get(vultr::get_block_storage))
        .route("/block-storage/:id", put(vultr::update_block_storage))
        .route("/block-storage/:id", delete(vultr::delete_block_storage))
        .route("/block-storage/:id/attach", post(vultr::attach_block_storage))
        .route("/block-storage/:id/detach", post(vultr::detach_block_storage))
        
        // Billing
        .route("/billing/invoices", get(vultr::list_invoices))
        .route("/billing/invoice-items", get(vultr::list_invoice_items))
        .route("/billing/pending-charges", get(vultr::get_pending_charges))
        
        // VPCs
        .route("/vpcs", get(vultr::list_vpcs))
        .route("/vpcs", post(vultr::create_vpc))
        .route("/vpcs/:id", get(vultr::get_vpc))
        .route("/vpcs/:id", put(vultr::update_vpc))
        .route("/vpcs/:id", delete(vultr::delete_vpc))
        
        // Regions and plans
        .route("/regions", get(vultr::list_regions))
        .route("/plans", get(vultr::list_plans))
        .route("/os", get(vultr::list_operating_systems))
        .route("/applications", get(vultr::list_applications))
}

fn webhook_routes() -> Router<Arc<AppState>> {
    Router::new()
        .route("/git", post(webhooks::handle_git_webhook))
        .route("/vultr", post(webhooks::handle_vultr_webhook))
        .route("/billing", post(webhooks::handle_billing_webhook))
}

async fn health_check() -> &'static str {
    "OK"
}
