use axum::{
    routing::{get, post, put, delete},
    Router,
};
use std::sync::Arc;
use crate::{
    AppState,
    controllers::{
        auth,
        applications,
        plans,
        regions,
    },
    middleware::{auth::auth_middleware, cors::cors_layer},
};

pub fn create_routes() -> Router<Arc<AppState>> {
    Router::new()
        // Health check
        .route("/health", get(health_check))
        
        // Public routes (no authentication required)
        .nest("/api/v1/public", public_routes())
        
        // User routes (authentication required)
        .nest("/api/v1/users", user_routes())

        // Admin routes (admin authentication required)
        .nest("/api/v1/admin", admin_routes())
        
        // Webhooks
        .nest("/webhooks", webhook_routes())
        
        .layer(cors_layer())
}

fn public_routes() -> Router<Arc<AppState>> {
    Router::new()
        .route("/auth/login", post(auth::login))
        .route("/auth/register", post(auth::register))
        .route("/auth/refresh", post(auth::refresh_token))
        .route("/regions", get(regions::list_regions))
        .route("/plans", get(plans::list_hosting_plans))
        .route("/plans/shared", get(plans::list_shared_plans))
        .route("/plans/dedicated", get(plans::list_dedicated_plans))
        .route("/plans/enterprise", get(plans::list_enterprise_plans))
}

fn user_routes() -> Router<Arc<AppState>> {
    Router::new()
        // Auth routes
        .route("/auth/me", get(auth::get_current_user))
        .route("/auth/logout", post(auth::logout))

        // Applications
        .route("/applications", get(applications::list_applications))
        .route("/applications", post(applications::create_application))
        .route("/applications/simple", post(applications::create_simple_application))
        .route("/applications/:id", get(applications::get_application))
        .route("/applications/:id", put(applications::update_application))
        .route("/applications/:id", delete(applications::delete_application))

}

fn admin_routes() -> Router<Arc<AppState>> {
    Router::new()
        // Admin-only routes will be implemented later
}

fn webhook_routes() -> Router<Arc<AppState>> {
    Router::new()
        // Webhook routes will be implemented later
}

async fn health_check() -> &'static str {
    "OK"
}
