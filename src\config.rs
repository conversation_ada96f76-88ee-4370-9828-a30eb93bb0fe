use serde::{Deserialize, Serialize};
use std::env;

#[derive(Debu<PERSON>, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct Config {
    pub server_address: String,
    pub database_url: String,
    pub vultr_api_key: String,
    pub jwt_secret: String,
    pub environment: Environment,
    pub log_level: String,
    pub jaeger_endpoint: Option<String>,
    pub metrics_port: u16,
    pub rate_limit_requests_per_second: u32,
    pub circuit_breaker_failure_threshold: u32,
    pub circuit_breaker_timeout_seconds: u64,
}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize, PartialEq)]
pub enum Environment {
    Development,
    Staging,
    Production,
}

impl Config {
    pub fn from_env() -> Result<Self, Box<dyn std::error::Error>> {
        dotenvy::dotenv().ok(); // Load .env file if it exists
        
        let environment = match env::var("ENVIRONMENT")
            .unwrap_or_else(|_| "development".to_string())
            .to_lowercase()
            .as_str()
        {
            "production" => Environment::Production,
            "staging" => Environment::Staging,
            _ => Environment::Development,
        };
        
        let config = Config {
            server_address: env::var("SERVER_ADDRESS")
                .unwrap_or_else(|_| "0.0.0.0:3000".to_string()),
            database_url: env::var("DATABASE_URL")
                .map_err(|_| "DATABASE_URL environment variable is required")?,
            vultr_api_key: env::var("VULTR_API_KEY")
                .map_err(|_| "VULTR_API_KEY environment variable is required")?,
            jwt_secret: env::var("JWT_SECRET")
                .map_err(|_| "JWT_SECRET environment variable is required")?,
            environment,
            log_level: env::var("LOG_LEVEL")
                .unwrap_or_else(|_| "info".to_string()),
            jaeger_endpoint: env::var("JAEGER_ENDPOINT").ok(),
            metrics_port: env::var("METRICS_PORT")
                .unwrap_or_else(|_| "9090".to_string())
                .parse()
                .unwrap_or(9090),
            rate_limit_requests_per_second: env::var("RATE_LIMIT_RPS")
                .unwrap_or_else(|_| "30".to_string())
                .parse()
                .unwrap_or(30),
            circuit_breaker_failure_threshold: env::var("CIRCUIT_BREAKER_FAILURE_THRESHOLD")
                .unwrap_or_else(|_| "5".to_string())
                .parse()
                .unwrap_or(5),
            circuit_breaker_timeout_seconds: env::var("CIRCUIT_BREAKER_TIMEOUT_SECONDS")
                .unwrap_or_else(|_| "60".to_string())
                .parse()
                .unwrap_or(60),
        };
        
        Ok(config)
    }
    
    pub fn is_development(&self) -> bool {
        self.environment == Environment::Development
    }
    
    pub fn is_production(&self) -> bool {
        self.environment == Environment::Production
    }
}
