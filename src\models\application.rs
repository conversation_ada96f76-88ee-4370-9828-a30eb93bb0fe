use serde::{Deserialize, Serialize};
use chrono::{DateTime, Utc};
use mongodb::bson::oid::ObjectId;
use std::collections::HashMap;
use crate::vultr::models::HostingPlan;

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct Application {
    #[serde(rename = "_id", skip_serializing_if = "Option::is_none")]
    pub id: Option<ObjectId>,
    pub user_id: ObjectId,
    pub name: String,
    pub description: Option<String>,
    pub repository_url: Option<String>,
    pub branch: String,
    pub build_command: Option<String>,
    pub install_command: Option<String>,
    pub start_command: Option<String>,
    pub environment_variables: HashMap<String, String>,
    pub service_type: ServiceType,
    pub status: super::ServiceStatus,
    pub hosting_plan: super::HostingPlan,
    pub region: String,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
    pub last_deployed_at: Option<DateTime<Utc>>,
    pub auto_deploy: bool,
    pub custom_domain: Option<String>,
    pub ssl_enabled: bool,
}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize, PartialEq)]
pub enum ServiceType {
    WebService,
    StaticSite,
    BackgroundWorker,
    CronJob,
    PrivateService,
}

#[derive(Debug, Deserialize)]
pub struct CreateApplicationRequest {
    pub name: String,
    pub description: Option<String>,
    pub repository_url: Option<String>,
    pub branch: Option<String>,
    pub build_command: Option<String>,
    pub install_command: Option<String>,
    pub start_command: Option<String>,
    pub environment_variables: Option<HashMap<String, String>>,
    pub service_type: ServiceType,
    pub hosting_plan: super::HostingPlan,
    pub region: Option<String>,
    pub auto_deploy: Option<bool>,
    pub custom_domain: Option<String>,
}

#[derive(Debug, Deserialize)]
pub struct CreateSimpleApplicationRequest {
    pub name: String,
    pub repository_url: String,
    pub branch: Option<String>,
    pub service_type: ServiceType,
}

#[derive(Debug, Deserialize)]
pub struct UpdateApplicationRequest {
    pub name: Option<String>,
    pub description: Option<String>,
    pub repository_url: Option<String>,
    pub branch: Option<String>,
    pub build_command: Option<String>,
    pub install_command: Option<String>,
    pub start_command: Option<String>,
    pub environment_variables: Option<HashMap<String, String>>,
    pub auto_deploy: Option<bool>,
    pub custom_domain: Option<String>,
}

#[derive(Debug, Serialize)]
pub struct ApplicationResponse {
    pub id: String,
    pub name: String,
    pub description: Option<String>,
    pub repository_url: Option<String>,
    pub branch: String,
    pub build_command: Option<String>,
    pub install_command: Option<String>,
    pub start_command: Option<String>,
    pub environment_variables: HashMap<String, String>,
    pub service_type: ServiceType,
    pub status: super::ServiceStatus,
    pub hosting_plan: super::HostingPlan,
    pub region: String,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
    pub last_deployed_at: Option<DateTime<Utc>>,
    pub auto_deploy: bool,
    pub custom_domain: Option<String>,
    pub ssl_enabled: bool,
    pub url: Option<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub enum ApplicationStatus {
    Draft,
    Building,
    Deploying,
    Running,
    Stopped,
    Failed,
    Suspended,
}

impl From<Application> for ApplicationResponse {
    fn from(app: Application) -> Self {
        let id = app.id.map(|id| id.to_hex()).unwrap_or_default();
        let url = if app.custom_domain.is_some() {
            app.custom_domain.clone()
        } else {
            Some(format!("https://{}.achidas.app", app.name))
        };

        Self {
            id,
            name: app.name,
            description: app.description,
            repository_url: app.repository_url,
            branch: app.branch,
            build_command: app.build_command,
            install_command: app.install_command,
            start_command: app.start_command,
            environment_variables: app.environment_variables,
            service_type: app.service_type,
            status: app.status,
            hosting_plan: app.hosting_plan,
            region: app.region,
            created_at: app.created_at,
            updated_at: app.updated_at,
            last_deployed_at: app.last_deployed_at,
            auto_deploy: app.auto_deploy,
            custom_domain: app.custom_domain,
            ssl_enabled: app.ssl_enabled,
            url,
        }
    }
}
