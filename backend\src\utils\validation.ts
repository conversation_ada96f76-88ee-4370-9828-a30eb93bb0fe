import Joi from 'joi';
import { ServiceType, HostingPlan } from '@/types';

// Password validation schema
const passwordSchema = Joi.string()
  .min(8)
  .max(128)
  .pattern(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/)
  .required()
  .messages({
    'string.min': 'Password must be at least 8 characters long',
    'string.max': 'Password cannot exceed 128 characters',
    'string.pattern.base': 'Password must contain at least one uppercase letter, one lowercase letter, one number, and one special character'
  });

// Email validation schema
const emailSchema = Joi.string()
  .email()
  .lowercase()
  .required()
  .messages({
    'string.email': 'Please provide a valid email address'
  });

// Username validation schema
const usernameSchema = Joi.string()
  .min(3)
  .max(30)
  .pattern(/^[a-zA-Z0-9_-]+$/)
  .required()
  .messages({
    'string.min': 'Username must be at least 3 characters long',
    'string.max': 'Username cannot exceed 30 characters',
    'string.pattern.base': 'Username can only contain letters, numbers, underscores, and hyphens'
  });

// Auth validation schemas
export const registerSchema = Joi.object({
  email: emailSchema,
  username: usernameSchema,
  password: passwordSchema,
  first_name: Joi.string().min(1).max(50).optional(),
  last_name: Joi.string().min(1).max(50).optional()
});

export const loginSchema = Joi.object({
  email: emailSchema,
  password: Joi.string().required().messages({
    'any.required': 'Password is required'
  })
});

export const refreshTokenSchema = Joi.object({
  refresh_token: Joi.string().required().messages({
    'any.required': 'Refresh token is required'
  })
});

// Application validation schemas
export const createApplicationSchema = Joi.object({
  name: Joi.string()
    .min(2)
    .max(100)
    .pattern(/^[a-zA-Z0-9\s\-_]+$/)
    .required()
    .messages({
      'string.min': 'Application name must be at least 2 characters long',
      'string.max': 'Application name cannot exceed 100 characters',
      'string.pattern.base': 'Application name can only contain letters, numbers, spaces, hyphens, and underscores'
    }),
  description: Joi.string().max(500).optional(),
  repository_url: Joi.string().uri().optional(),
  branch: Joi.string().max(100).default('main'),
  build_command: Joi.string().max(500).optional(),
  install_command: Joi.string().max(500).optional(),
  start_command: Joi.string().max(500).optional(),
  environment_variables: Joi.object().pattern(
    Joi.string(),
    Joi.string()
  ).optional(),
  service_type: Joi.string()
    .valid(...Object.values(ServiceType))
    .required(),
  hosting_plan: Joi.string()
    .valid(...Object.values(HostingPlan))
    .required(),
  region: Joi.string().max(50).optional(),
  auto_deploy: Joi.boolean().default(false),
  custom_domain: Joi.string()
    .pattern(/^[a-zA-Z0-9][a-zA-Z0-9-]{1,61}[a-zA-Z0-9]\.[a-zA-Z]{2,}$/)
    .optional()
    .messages({
      'string.pattern.base': 'Please provide a valid domain name'
    })
});

export const createSimpleApplicationSchema = Joi.object({
  name: Joi.string()
    .min(2)
    .max(100)
    .pattern(/^[a-zA-Z0-9\s\-_]+$/)
    .required(),
  repository_url: Joi.string().uri().required(),
  branch: Joi.string().max(100).default('main'),
  service_type: Joi.string()
    .valid(...Object.values(ServiceType))
    .required()
});

export const updateApplicationSchema = Joi.object({
  name: Joi.string()
    .min(2)
    .max(100)
    .pattern(/^[a-zA-Z0-9\s\-_]+$/)
    .optional(),
  description: Joi.string().max(500).optional(),
  repository_url: Joi.string().uri().optional(),
  branch: Joi.string().max(100).optional(),
  build_command: Joi.string().max(500).optional(),
  install_command: Joi.string().max(500).optional(),
  start_command: Joi.string().max(500).optional(),
  environment_variables: Joi.object().pattern(
    Joi.string(),
    Joi.string()
  ).optional(),
  auto_deploy: Joi.boolean().optional(),
  custom_domain: Joi.string()
    .pattern(/^[a-zA-Z0-9][a-zA-Z0-9-]{1,61}[a-zA-Z0-9]\.[a-zA-Z]{2,}$/)
    .optional()
});

// Pagination validation schema
export const paginationSchema = Joi.object({
  page: Joi.number().integer().min(1).default(1),
  limit: Joi.number().integer().min(1).max(100).default(10),
  sort: Joi.string().optional(),
  order: Joi.string().valid('asc', 'desc').default('desc')
});

// MongoDB ObjectId validation
export const objectIdSchema = Joi.string()
  .pattern(/^[0-9a-fA-F]{24}$/)
  .required()
  .messages({
    'string.pattern.base': 'Invalid ID format'
  });

// Validation helper function
export const validateSchema = (schema: Joi.Schema, data: any) => {
  const { error, value } = schema.validate(data, {
    abortEarly: false,
    stripUnknown: true
  });

  if (error) {
    const errorMessage = error.details.map(detail => detail.message).join(', ');
    throw new Error(errorMessage);
  }

  return value;
};
