pub mod user;
pub mod application;

use serde::{Deserialize, Serialize};
use chrono::{DateTime, Utc};
use mongodb::bson::oid::ObjectId;

// Re-export all models
pub use user::*;
pub use application::*;

/// Standard API response wrapper
#[derive(Debug, Serialize, Deserialize)]
pub struct ApiResponse<T> {
    pub success: bool,
    pub data: Option<T>,
    pub error: Option<String>,
    pub metadata: Option<ResponseMetadata>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct ResponseMetadata {
    pub timestamp: DateTime<Utc>,
    pub request_id: String,
    pub version: String,
    pub pagination: Option<PaginationInfo>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct PaginationInfo {
    pub page: u32,
    pub per_page: u32,
    pub total: u64,
    pub total_pages: u32,
}

/// Standard pagination query parameters
#[derive(Debug, Deserialize)]
pub struct PaginationQuery {
    pub page: Option<u32>,
    pub per_page: Option<u32>,
}

impl Default for PaginationQuery {
    fn default() -> Self {
        Self {
            page: Some(1),
            per_page: Some(20),
        }
    }
}

/// Build priority levels
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub enum BuildPriority {
    Low,
    Normal,
    High,
    Critical,
}

/// Service status enumeration
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub enum ServiceStatus {
    Pending,
    Building,
    Deploying,
    Running,
    Stopped,
    Failed,
    Maintenance,
}

/// Payment status for users
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub enum PaymentStatus {
    Active,
    Suspended,
    Overdue,
    Cancelled,
}

/// User state in shared hosting environment
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
#[serde(rename_all = "snake_case")]
pub enum UserState {
    Hot,    // Active, paid user
    Cold,   // Inactive or unpaid user
    Frozen, // Temporarily suspended
    Migrating, // Being moved between servers
}

impl From<UserState> for mongodb::bson::Bson {
    fn from(state: UserState) -> Self {
        match state {
            UserState::Hot => mongodb::bson::Bson::String("hot".to_string()),
            UserState::Cold => mongodb::bson::Bson::String("cold".to_string()),
            UserState::Frozen => mongodb::bson::Bson::String("frozen".to_string()),
            UserState::Migrating => mongodb::bson::Bson::String("migrating".to_string()),
        }
    }
}

/// Hosting plan types
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub enum HostingPlan {
    Shared,
    Dedicated,
    Enterprise,
}

/// Server resource state
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ServerResourceState {
    pub cpu_usage_percent: f64,
    pub memory_usage_percent: f64,
    pub disk_usage_percent: f64,
    pub network_usage_mbps: f64,
    pub active_users: u32,
    pub total_users: u32,
    pub load_average: f64,
}

/// Generic error response
#[derive(Debug, Serialize, Deserialize)]
pub struct ErrorResponse {
    pub error: String,
    pub code: Option<String>,
    pub details: Option<serde_json::Value>,
}

/// Health check response
#[derive(Debug, Serialize, Deserialize)]
pub struct HealthResponse {
    pub status: String,
    pub timestamp: DateTime<Utc>,
    pub version: String,
    pub services: Vec<ServiceHealth>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct ServiceHealth {
    pub name: String,
    pub status: String,
    pub response_time_ms: Option<u64>,
}

/// Common validation traits
pub trait Validatable {
    fn validate(&self) -> Result<(), String>;
}

/// Common database document traits
pub trait Document {
    fn id(&self) -> Option<&ObjectId>;
    fn created_at(&self) -> &DateTime<Utc>;
    fn updated_at(&self) -> &DateTime<Utc>;
}
