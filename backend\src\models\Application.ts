import mongoose, { Document, Schema, Types } from 'mongoose';
import { ServiceType, ApplicationStatus, HostingPlan } from '@/types';

export interface IApplication extends Document {
  _id: Types.ObjectId;
  user_id: mongoose.Types.ObjectId;
  name: string;
  description?: string;
  repository_url?: string;
  branch: string;
  build_command?: string;
  install_command?: string;
  start_command?: string;
  environment_variables: Record<string, string>;
  service_type: ServiceType;
  hosting_plan: HostingPlan;
  status: ApplicationStatus;
  region?: string;
  auto_deploy: boolean;
  custom_domain?: string;
  deployment_url?: string;
  server_id?: string;
  container_id?: string;
  port?: number;
  ssl_enabled: boolean;
  created_at: Date;
  updated_at: Date;
  last_deployed?: Date;
  build_logs?: string[];
  deployment_logs?: string[];
  resource_usage?: {
    cpu_percent: number;
    memory_mb: number;
    disk_mb: number;
    bandwidth_mb: number;
  };
}

const applicationSchema = new Schema<IApplication>({
  user_id: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: [true, 'User ID is required'],
    index: true
  },
  name: {
    type: String,
    required: [true, 'Application name is required'],
    trim: true,
    minlength: [2, 'Application name must be at least 2 characters'],
    maxlength: [100, 'Application name cannot exceed 100 characters'],
    match: [/^[a-zA-Z0-9\s\-_]+$/, 'Application name can only contain letters, numbers, spaces, hyphens, and underscores']
  },
  description: {
    type: String,
    trim: true,
    maxlength: [500, 'Description cannot exceed 500 characters']
  },
  repository_url: {
    type: String,
    trim: true,
    match: [/^https?:\/\/.+/, 'Repository URL must be a valid HTTP/HTTPS URL']
  },
  branch: {
    type: String,
    default: 'main',
    trim: true,
    maxlength: [100, 'Branch name cannot exceed 100 characters']
  },
  build_command: {
    type: String,
    trim: true,
    maxlength: [500, 'Build command cannot exceed 500 characters']
  },
  install_command: {
    type: String,
    trim: true,
    maxlength: [500, 'Install command cannot exceed 500 characters']
  },
  start_command: {
    type: String,
    trim: true,
    maxlength: [500, 'Start command cannot exceed 500 characters']
  },
  environment_variables: {
    type: Map,
    of: String,
    default: new Map()
  },
  service_type: {
    type: String,
    enum: Object.values(ServiceType),
    required: [true, 'Service type is required']
  },
  hosting_plan: {
    type: String,
    enum: Object.values(HostingPlan),
    required: [true, 'Hosting plan is required']
  },
  status: {
    type: String,
    enum: Object.values(ApplicationStatus),
    default: ApplicationStatus.PENDING
  },
  region: {
    type: String,
    trim: true,
    maxlength: [50, 'Region cannot exceed 50 characters']
  },
  auto_deploy: {
    type: Boolean,
    default: false
  },
  custom_domain: {
    type: String,
    trim: true,
    lowercase: true,
    match: [/^[a-zA-Z0-9][a-zA-Z0-9-]{1,61}[a-zA-Z0-9]\.[a-zA-Z]{2,}$/, 'Please enter a valid domain name']
  },
  deployment_url: {
    type: String,
    trim: true
  },
  server_id: {
    type: String,
    trim: true
  },
  container_id: {
    type: String,
    trim: true
  },
  port: {
    type: Number,
    min: [1000, 'Port must be at least 1000'],
    max: [65535, 'Port cannot exceed 65535']
  },
  ssl_enabled: {
    type: Boolean,
    default: false
  },
  created_at: {
    type: Date,
    default: Date.now
  },
  updated_at: {
    type: Date,
    default: Date.now
  },
  last_deployed: {
    type: Date
  },
  build_logs: [{
    type: String
  }],
  deployment_logs: [{
    type: String
  }],
  resource_usage: {
    cpu_percent: {
      type: Number,
      min: 0,
      max: 100,
      default: 0
    },
    memory_mb: {
      type: Number,
      min: 0,
      default: 0
    },
    disk_mb: {
      type: Number,
      min: 0,
      default: 0
    },
    bandwidth_mb: {
      type: Number,
      min: 0,
      default: 0
    }
  }
}, {
  timestamps: { createdAt: 'created_at', updatedAt: 'updated_at' },
  toJSON: {
    transform: function(doc, ret) {
      ret.id = ret._id;
      delete ret._id;
      delete ret.__v;
      
      // Convert Map to Object for environment_variables
      if (ret.environment_variables instanceof Map) {
        ret.environment_variables = Object.fromEntries(ret.environment_variables);
      }
      
      return ret;
    }
  }
});

// Indexes
applicationSchema.index({ user_id: 1 });
applicationSchema.index({ name: 1 });
applicationSchema.index({ status: 1 });
applicationSchema.index({ service_type: 1 });
applicationSchema.index({ hosting_plan: 1 });
applicationSchema.index({ created_at: -1 });
applicationSchema.index({ user_id: 1, name: 1 }, { unique: true }); // Unique name per user

// Pre-save middleware to update timestamps
applicationSchema.pre('save', function(next) {
  this.updated_at = new Date();
  next();
});

export const Application = mongoose.model<IApplication>('Application', applicationSchema);
