use achidas::{
    config::Config,
    infrastructure::{
        database::Database,
        state_machine::StateMachine,
        circuit_breaker::CircuitBreakerRegistry,
        rate_limiter::RateLimiter,
        metrics::MetricsCollector,
        chunk_processor::ChunkProcessor,
        database_indexes::DatabaseIndexManager,
    },
    observability,
    routes::create_routes,
    services::{
        auth::AuthService,
        billing::BillingService,
        blueprint::BlueprintService,
        build::BuildService,
        deployment::DeploymentService,
        disk::DiskService,
        domain::DomainService,
        environment::EnvironmentService,
        git::GitService,
        instance::InstanceService,
        intelligent_hosting::IntelligentHostingService,
        kubernetes_deployment::KubernetesDeploymentService,
        shared_hosting::SharedHostingService,
        shared_hosting_orchestrator::SharedHostingOrchestrator,
        state_management::StateManagementService,
        ssh_key_management::SSHKeyManagementService,
        resource_allocation::ResourceAllocationService,
        load_balancing::LoadBalancingService,
        payment_access_control::PaymentAccessControlService,
        activity_monitoring::ActivityMonitoringService,
        server_manager::ServerManagerService,
    },
    vultr::VultrClient,
    AppState,
};
use axum::Router;
use std::sync::Arc;
use tokio::net::TcpListener;
use tower::ServiceBuilder;
use tower_http::{
    cors::CorsLayer,
    timeout::TimeoutLayer,
    trace::TraceLayer,
};
use tracing::{info, instrument};
use std::time::Duration;

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    // Initialize configuration
    let config = Config::from_env()?;
    
    // Initialize observability
    observability::init(&config).await?;
    
    info!("Starting Achidas Cloud Platform");
    
    // Initialize database
    let database = Database::new(&config.database_url).await?;
    
    // Initialize database indexes
    let index_manager = DatabaseIndexManager::new(database.clone());
    index_manager.create_all_indexes().await?;
    
    // Initialize Vultr client
    let vultr_client = VultrClient::new(&config.vultr_api_key)?;
    
    // Initialize services
    let auth_service = AuthService::new(database.clone(), &config.jwt_secret);
    let billing_service = BillingService::new(database.clone(), vultr_client.clone());
    let blueprint_service = BlueprintService::new(database.clone());
    let build_service = BuildService::new(database.clone());
    let deployment_service = DeploymentService::new(database.clone(), build_service.clone());
    let disk_service = DiskService::new(database.clone(), vultr_client.clone());
    let domain_service = DomainService::new(database.clone(), vultr_client.clone());
    let environment_service = EnvironmentService::new(database.clone());
    let git_service = GitService::new(database.clone());
    let instance_service = InstanceService::new(database.clone(), vultr_client.clone());
    let intelligent_hosting_service = IntelligentHostingService::new(database.clone(), vultr_client.clone());
    let kubernetes_deployment_service = KubernetesDeploymentService::new(database.clone());
    let shared_hosting_service = SharedHostingService::new(database.clone());
    let shared_hosting_orchestrator = SharedHostingOrchestrator::new(database.clone());
    let state_management_service = StateManagementService::new(database.clone());
    let ssh_key_management_service = SSHKeyManagementService::new(database.clone());
    let resource_allocation_service = ResourceAllocationService::new(database.clone());
    let load_balancing_service = LoadBalancingService::new(database.clone());
    let payment_access_control_service = PaymentAccessControlService::new(database.clone());
    let activity_monitoring_service = ActivityMonitoringService::new(database.clone());
    let server_manager_service = ServerManagerService::new(database.clone(), vultr_client.clone());
    
    // Initialize infrastructure components
    let circuit_breaker_registry = CircuitBreakerRegistry::new();
    let rate_limiter = RateLimiter::new();
    let metrics_collector = MetricsCollector::new();
    let chunk_processor = ChunkProcessor::new();
    let state_machine = StateMachine::new();
    
    // Create application state
    let app_state = Arc::new(AppState {
        config: config.clone(),
        database,
        vultr_client,
        auth_service,
        billing_service,
        blueprint_service,
        build_service,
        deployment_service,
        disk_service,
        domain_service,
        environment_service,
        git_service,
        instance_service,
        intelligent_hosting_service,
        kubernetes_deployment_service,
        shared_hosting_service,
        shared_hosting_orchestrator,
        state_management_service,
        ssh_key_management_service,
        resource_allocation_service,
        load_balancing_service,
        payment_access_control_service,
        activity_monitoring_service,
        server_manager_service,
        circuit_breaker_registry,
        rate_limiter,
        metrics_collector,
        chunk_processor,
        state_machine,
    });
    
    // Create router with middleware
    let app = create_routes()
        .with_state(app_state)
        .layer(
            ServiceBuilder::new()
                .layer(TraceLayer::new_for_http())
                .layer(CorsLayer::permissive())
                .layer(TimeoutLayer::new(Duration::from_secs(30)))
        );
    
    // Start server
    let listener = TcpListener::bind(&config.server_address).await?;
    info!("Server listening on {}", config.server_address);
    
    axum::serve(listener, app).await?;
    
    Ok(())
}
