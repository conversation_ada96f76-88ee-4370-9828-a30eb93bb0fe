use achidas::{
    config::Config,
    infrastructure::{
        database::Database,
        circuit_breaker::CircuitBreakerRegistry,
    },
    observability,
    routes::create_routes,
    services::auth::AuthService,
    vultr::VultrApiClient,
    AppState,
};
use axum::Router;
use std::sync::Arc;
use tokio::net::TcpListener;
use tower::ServiceBuilder;
use tower_http::{
    cors::CorsLayer,
    timeout::TimeoutLayer,
    trace::TraceLayer,
};
use tracing::{info, instrument};
use std::time::Duration;

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    // Initialize configuration
    let config = Config::from_env()?;
    
    // Initialize observability
    observability::init(&config).await?;
    
    info!("Starting Achidas Cloud Platform");
    
    // Initialize database
    let database = Database::new(&config.database_url).await?;
    
    // Initialize Vultr client
    let vultr_client = VultrApiClient::new(&config.vultr_api_key)?;

    // Initialize services
    let auth_service = AuthService::new(database.clone(), &config.jwt_secret);

    // Initialize infrastructure components
    let circuit_breaker_registry = CircuitBreakerRegistry::new();
    
    // Create application state
    let app_state = Arc::new(AppState {
        config: config.clone(),
        database,
        vultr_client,
        auth_service,
        circuit_breaker_registry,
    });
    
    // Create router with middleware
    let app = create_routes()
        .with_state(app_state)
        .layer(
            ServiceBuilder::new()
                .layer(TraceLayer::new_for_http())
                .layer(TimeoutLayer::new(Duration::from_secs(30)))
        );
    
    // Start server
    let listener = TcpListener::bind(&config.server_address).await?;
    info!("Server listening on {}", config.server_address);
    
    axum::serve(listener, app).await?;
    
    Ok(())
}
